import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/base_home_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

class HomePanelsController extends BaseHomeController {
  HomePanelsController(AuthService authService, HomeStateManager state) : super(authService, state);

  final _panelController = PanelController();

  final _isPanelDraggable = true.obs;
  final _isPanelVisible = true.obs;
  final _screenIndex = 0.obs;

  bool get isPanelDraggable => _isPanelDraggable.value;
  bool get isPanelVisible => _isPanelVisible.value;
  int get screenIndex => _screenIndex.value;
  PanelController get panelController => _panelController;

  set isPanelDraggable(bool value) => _isPanelDraggable.value = value;
  set isPanelVisible(bool value) => _isPanelVisible.value = value;
  set screenIndex(int value) => _screenIndex.value = value;

  // Navigation methods
  void goToFeedPage() {
    screenIndex = 0;
    panelController.open();
    isPanelDraggable = true;
  }

  void goToSearchPage() {
    screenIndex = 1;
    panelController.open();
    isPanelDraggable = false;
  }

  void goToFilterPage() {
    screenIndex = 2;
    panelController.open();
    isPanelDraggable = false;
  }

  // Panel methods
  void setPanelOpen() => isPanelVisible = true;
  void setPanelClosed() => isPanelVisible = false;
}
