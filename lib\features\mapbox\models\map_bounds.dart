import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';

class MapBounds {
  final double latStart;
  final double latEnd;
  final double lngStart;
  final double lngEnd;
  final double latRange;
  final double lngRange;
  final double latCenter;
  final double lngCenter;

  MapBounds({
    required this.latStart,
    required this.latEnd,
    required this.lngStart,
    required this.lngEnd,
  })  : latRange = latEnd - latStart,
        lngRange = lngEnd - lngStart,
        latCenter = (latStart + latEnd) / 2,
        lngCenter = (lngStart + lngEnd) / 2;

  factory MapBounds.fromCoordinateBounds(CoordinateBounds bounds) {
    return MapBounds(
      latStart: bounds.southwest.coordinates.lat.toDouble(),
      latEnd: bounds.northeast.coordinates.lat.toDouble(),
      lngStart: bounds.southwest.coordinates.lng.toDouble(),
      lngEnd: bounds.northeast.coordinates.lng.toDouble(),
    );
  }

  @override
  String toString() {
    return 'MapBounds(latStart: $latStart, latEnd: $latEnd, lngStart: $lngStart, lngEnd: $lngEnd)';
  }
}
