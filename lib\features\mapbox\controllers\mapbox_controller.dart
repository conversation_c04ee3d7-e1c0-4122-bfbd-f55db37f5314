import 'package:geolocator/geolocator.dart' as geo;
import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/mapbox/controllers/marker_controller.dart';
import 'package:ivent_app/features/mapbox/models/ia_location_item.dart';
import 'package:ivent_app/features/mapbox/models/map_bounds.dart';
import 'package:ivent_app/features/mapbox/models/marker_feature.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:permission_handler/permission_handler.dart';

class MapboxController {
  final AuthService authService;

  final Function(List<MarkerFeature> selectedFeatures)? onSelectedFeaturesChanged;

  MapboxController({required this.authService, this.onSelectedFeaturesChanged}) {
    markerController = MarkerController(onSelectedFeaturesChanged: onSelectedFeaturesChanged);
    getUserLocationCoordinates();
  }

  late final MarkerController markerController;
  late final MapboxMap mapboxMap;
  bool isMapboxMapInitialized = false;

  final _currentMapBounds = Rxn<MapBounds>();
  final _userLocationCoordinates = Rxn<Position>();
  final _userLocation = Rxn<IaLocationItem>();

  MapBounds? get currentMapBounds => _currentMapBounds.value;
  Position? get userLocationCoordinates => _userLocationCoordinates.value;
  IaLocationItem? get userLocation => _userLocation.value;

  set userLocationCoordinates(Position? value) => _userLocationCoordinates.value = value;
  set userLocation(IaLocationItem? value) => _userLocation.value = value;

  void setMapboxMap(MapboxMap mapboxMap) {
    if (isMapboxMapInitialized) return;
    this.mapboxMap = mapboxMap;
    markerController.setMapboxMap(mapboxMap);
    isMapboxMapInitialized = true;
  }

  Future<void> getUserLocationCoordinates() async {
    final status = await Permission.locationWhenInUse.request();
    if (!status.isGranted) return;

    final position = await geo.Geolocator.getCurrentPosition();
    userLocationCoordinates = Position(position.longitude, position.latitude);
    await _getUserLocationInfo();
  }

  Future<void> _getUserLocationInfo() async {
    final result = await authService.mapboxApi.searchBoxReverse(
      userLocationCoordinates!.lng.toDouble(),
      userLocationCoordinates!.lat.toDouble(),
    );

    if (result != null && result.features.isNotEmpty) {
      userLocation = IaLocationItem.fromProperties(result.features.first.properties);
    }
  }

  Future<void> moveCameraToUserLocation() async {
    if (!isMapboxMapInitialized) return;
    getUserLocationCoordinates();
    if (userLocationCoordinates == null) return;
    await mapboxMap.setCamera(CameraOptions(center: Point(coordinates: userLocationCoordinates!), zoom: 14));
  }

  Future<void> updateVisibleMapBounds() async {
    if (!isMapboxMapInitialized) return;
    final newCameraState = await mapboxMap.getCameraState();
    final cameraOptions = CameraOptions(
      center: newCameraState.center,
      zoom: newCameraState.zoom,
      bearing: newCameraState.bearing,
      pitch: newCameraState.pitch,
    );
    final bounds = await mapboxMap.coordinateBoundsForCamera(cameraOptions);
    _currentMapBounds.value = MapBounds.fromCoordinateBounds(bounds);
    print('Current map bounds: $_currentMapBounds');
  }
}
