import 'package:flutter/material.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/features/profile/controllers/profile_controller.dart';
import 'package:ivent_app/features/profile/widgets/vibe_thumbnail.dart';

class ProfileTabs extends StatelessWidget {
  final ProfileController controller;
  final GetVibeFoldersByUserIdReturn? vibesContext;

  const ProfileTabs({
    super.key,
    required this.controller,
    required this.vibesContext,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: AppDimensions.padding20, bottom: 100),
      child: Tab<PERSON>arView(
        physics: const NeverScrollableScrollPhysics(),
        children: [
          _buildVibesTab(),
          _buildMemoriesTab(),
        ],
      ),
    );
  }

  Widget _buildVibesTab() {
    return GridView.builder(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: AppDimensions.padding16,
        mainAxisSpacing: AppDimensions.padding16,
        childAspectRatio: 0.6,
      ),
      itemCount: vibesContext!.vibeFolders.length + 1,
      itemBuilder: (context, index) {
        if (controller.authService.hasUpcomingEventToday) {
          if (index == 0) {
            // final upcomingEvent = controller.authService.!;
            // return VibeThumbnail(
            //   iventId: upcomingEvent.iventId,
            //   iventName: upcomingEvent.iventName,
            //   date: upcomingEvent.date,
            //   participantCount: upcomingEvent.memberCount,
            //   participantNames: upcomingEvent.memberNames,
            // );
          } else {
            final vibe = vibesContext!.vibeFolders[index - 1];
            return VibeThumbnail(
              vibeId: vibe.vibeId,
              iventName: vibe.iventName,
              participantCount: vibe.memberCount,
              participantNames: vibe.memberFirstnames,
              imageUrl: vibe.thumbnailUrl,
            );
          }
        } else {
          final vibe = vibesContext!.vibeFolders[index];
          return VibeThumbnail(
            vibeId: vibe.vibeId,
            iventName: vibe.iventName,
            participantCount: vibe.memberCount,
            participantNames: vibe.memberFirstnames,
            imageUrl: vibe.thumbnailUrl,
          );
        }
      },
    );
  }

  Container _buildMemoriesTab() {
    return Container(
      child: const Center(child: Text('Content for Tab 2')),
    );
  }
}
