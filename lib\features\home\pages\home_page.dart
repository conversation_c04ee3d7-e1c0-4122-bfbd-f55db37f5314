import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/widgets/layout/panels/ia_bottom_panel.dart';
import 'package:ivent_app/core/widgets/layout/panels/ia_sliding_panel.dart';
import 'package:ivent_app/features/home/<USER>/home_controller.dart';
import 'package:ivent_app/features/home/<USER>/feed_screen.dart';
import 'package:ivent_app/features/home/<USER>/filter_screen.dart';
import 'package:ivent_app/features/home/<USER>/map_screen.dart';
import 'package:ivent_app/features/home/<USER>/search_screen.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  late final HomeController _controller;
  final List _screens = [
    const FeedScreen(),
    const SearchScreen(),
    const FilterScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _controller = Get.find();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        return IaSlidingPanel(
          defaultPanelState: PanelState.CLOSED,
          panelController: _controller.homePanelsController.panelController,
          isDraggable: _controller.homePanelsController.isPanelDraggable,
          maxHeight: Get.height,
          minHeight: 70,
          onPanelOpened: _controller.homePanelsController.setPanelOpen,
          onPanelClosed: _controller.homePanelsController.setPanelClosed,
          panel: IaBottomPanel(
            showSlideIndicator: _controller.homePanelsController.isPanelDraggable,
            body: _screens[_controller.homePanelsController.screenIndex],
          ),
          body: const SafeArea(child: MapScreen()),
        );
      }),
    );
  }
}
