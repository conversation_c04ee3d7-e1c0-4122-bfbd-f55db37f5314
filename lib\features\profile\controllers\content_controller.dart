import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

class ContentController extends BaseControllerWithSharedState<ProfileStateManager> {
  ContentController(AuthService authService, ProfileStateManager state) : super(authService, state);

  final _vibeFolders = Rx<GetVibeFoldersByUserIdReturn?>(null);
  final _memoryFolders = Rx<GetMemoryFoldersByUserIdReturn?>(null);

  GetVibeFoldersByUserIdReturn? get vibeFolders => _vibeFolders.value;
  GetMemoryFoldersByUserIdReturn? get memoryFolders => _memoryFolders.value;

  set vibeFolders(GetVibeFoldersByUserIdReturn? value) => _vibeFolders.value = value;
  set memoryFolders(GetMemoryFoldersByUserIdReturn? value) => _memoryFolders.value = value;

  @override
  Future<void> initController() async {
    super.initController();
    await loadFolders();
  }

  Future<void> loadFolders() async {
    vibeFolders = await authService.usersApi.getVibeFoldersByUserId(state.userId);
  }
}
