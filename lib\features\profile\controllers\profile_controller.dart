import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/content_controller.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/features/profile/controllers/searchable/favorites_controller.dart';
import 'package:ivent_app/features/profile/controllers/searchable/followers_controller.dart';
import 'package:ivent_app/features/profile/controllers/searchable/followings_controller.dart';
import 'package:ivent_app/features/profile/controllers/searchable/friends_controller.dart';
import 'package:ivent_app/features/profile/controllers/searchable/ivents_controller.dart';
import 'package:ivent_app/features/profile/controllers/social_controller.dart';
import 'package:ivent_app/features/profile/controllers/user_info_controller.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

class ProfileController extends BaseControllerWithSharedState<ProfileStateManager> {
  ProfileController(AuthService authService, ProfileStateManager state) : super(authService, state);

  // Child controllers
  late final UserInfoController userInfoController;
  late final ContentController contentController;
  late final SocialController socialController;
  late final IventsController iventsController;
  late final FriendsController friendsController;
  late final FollowersController followersController;
  late final FollowingsController followingsController;
  late final FavoritesController favoritesController;

  @override
  void initController() async {
    super.initController();
    userInfoController = Get.put(UserInfoController(authService, state), tag: state.userId);
    contentController = Get.put(ContentController(authService, state), tag: state.userId);
    socialController = Get.put(SocialController(authService, state), tag: state.userId);
    iventsController = Get.put(IventsController(authService, state), tag: state.userId);
    friendsController = Get.put(FriendsController(authService, state), tag: state.userId);
    followersController = Get.put(FollowersController(authService, state), tag: state.userId);
    followingsController = Get.put(FollowingsController(authService, state), tag: state.userId);
    favoritesController = Get.put(FavoritesController(authService, state), tag: state.userId);
    print('ProfileController has been initialized with user: ${sessionUser.sessionId} and userId: $state.userId');
  }

  @override
  void closeController() {
    Get.delete<UserInfoController>(tag: state.userId);
    Get.delete<ContentController>(tag: state.userId);
    Get.delete<SocialController>(tag: state.userId);
    Get.delete<IventsController>(tag: state.userId);
    Get.delete<FriendsController>(tag: state.userId);
    Get.delete<FollowersController>(tag: state.userId);
    Get.delete<FollowingsController>(tag: state.userId);
    Get.delete<FavoritesController>(tag: state.userId);
    super.closeController();
  }

  Future<void> goToIventsPage() async => await iventsController.goToIventsPage();
  Future<void> goToFriendsPage() async => await friendsController.goToFriendsPage();
  Future<void> goToFollowersPage() async => await followersController.goToFollowersPage();
  Future<void> goToFollowingsPage() async => await followingsController.goToFollowingsPage();
  Future<void> goToFavoritesPage() async => await favoritesController.goToFavoritesPage();
}
