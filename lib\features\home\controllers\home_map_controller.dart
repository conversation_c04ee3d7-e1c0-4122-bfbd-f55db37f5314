import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/base_home_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';
import 'package:ivent_app/features/home/<USER>/remove_duplicate_coordinates.dart';
import 'package:ivent_app/features/mapbox/controllers/mapbox_controller.dart';
import 'package:ivent_app/features/mapbox/controllers/marker_controller.dart';
import 'package:ivent_app/features/mapbox/models/marker_feature.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';

class HomeMapController extends BaseHomeController {
  HomeMapController(AuthService authService, HomeStateManager state) : super(authService, state) {
    mapboxController = MapboxController(authService: authService, onSelectedFeaturesChanged: onSelectedFeaturesChanged);
  }

  late final MapboxController mapboxController;

  final _iventBanners = <IventCardItem>[].obs;
  final _mapReturn = Rxn<MapReturn>();

  List<IventCardItem> get iventBanners => _iventBanners;
  MapReturn? get mapReturn => _mapReturn.value;
  MarkerController get markerController => mapboxController.markerController;

  set iventBanners(List<IventCardItem> value) => _iventBanners.assignAll(value);
  set mapReturn(MapReturn? value) => _mapReturn.value = value;

  String get todayAsString => '${DateFormat('d MMMM EEEE').format(DateTime.now())}';

  @override
  void initController() async {
    super.initController();
    await mapboxController.getUserLocationCoordinates();
  }

  Future<void> onSelectedFeaturesChanged(List<MarkerFeature> selectedFeatures) async {
    if (selectedFeatures.isEmpty) {
      iventBanners = [];
      return;
    }
    final iventIds = selectedFeatures.map((feature) => feature.id).toList();
    final getBannerByIventIdResult = await authService.iventsApi.getBannerByIventId(
      GetBannerByIventIdDto(iventIds: iventIds),
    );
    if (getBannerByIventIdResult == null) return;
    iventBanners = getBannerByIventIdResult.ivents;
  }

  Future<void> updateVisibleMapBounds({CameraState? cameraState}) async {
    await mapboxController.updateVisibleMapBounds();
    await loadMarkers();
  }

  Future<void> loadMarkers() async {
    if (mapboxController.currentMapBounds == null) return;
    final bounds = mapboxController.currentMapBounds!;
    mapReturn = await authService.homeApi.map(
      DateTime.parse('2018-01-01T00:00:00Z').toIso8601String(), // 1st January 2018
      DateTime.now().add(const Duration(days: 365 * 5)).toIso8601String(), // 5 years
      bounds.latStart,
      bounds.latEnd,
      bounds.lngStart,
      bounds.lngEnd,
    );
    if (mapReturn == null) return;
    final result = mapReturn!.ivents;
    List<MarkerFeature> markersToBeAdded = result.map((val) {
      return MarkerFeature(
        id: val.iventId,
        properties: MarkerFeatureProperties(isSelected: false),
        geometry: MarkerFeatureGeometry(coordinates: [val.longitude, val.latitude]),
      );
    }).toList();
    markersToBeAdded = removeDuplicateCoordinates(markersToBeAdded, exclude: markerController.allFeatures);
    markerController.addMarkers(markersToBeAdded);
  }
}
