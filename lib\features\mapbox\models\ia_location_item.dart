import 'package:ivent_app/api/api.dart';

class IaLocationItem {
  final String? mapboxId;
  final String? locationId;
  final String name;
  final String address;
  final double latitude;
  final double longitude;

  IaLocationItem({
    required this.mapboxId,
    required this.locationId,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
  }) : assert(mapboxId != null || locationId != null);

  factory IaLocationItem.fromProperties(SearchBoxProperties properties) {
    return IaLocationItem(
      mapboxId: properties.mapboxId,
      locationId: properties.mapboxId,
      name: properties.name,
      address: properties.fullAddress ?? properties.placeFormatted,
      latitude: properties.coordinates.latitude,
      longitude: properties.coordinates.longitude,
    );
  }
}
