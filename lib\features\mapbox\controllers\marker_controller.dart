import 'package:get/get.dart';
import 'package:ivent_app/core/utils/extension_utils.dart';
import 'package:ivent_app/features/mapbox/models/marker_feature.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';

class MarkerController {
  final Function(List<MarkerFeature> selectedFeatures)? onSelectedFeaturesChanged;

  MarkerController({this.onSelectedFeaturesChanged});

  final String sourceId = 'clustered-points';

  late final MapboxMap mapboxMap;
  bool isMapboxMapInitialized = false;

  final _allFeatures = <MarkerFeature>[].obs;
  final _selectedFeatures = <MarkerFeature>[].obs;

  List<MarkerFeature> get allFeatures => _allFeatures;
  List<MarkerFeature> get selectedFeatures => _selectedFeatures;
  List<String> get selectedIventIds => selectedFeatures.map((e) => e.id).toList();

  void setMapboxMap(MapboxMap mapboxMap) {
    if (isMapboxMapInitialized) return;
    this.mapboxMap = mapboxMap;
    isMapboxMapInitialized = true;
  }

  Future<void> handleFeatureClick(final String? layerId, final Map<String?, Object?>? feature) async {
    if (!isMapboxMapInitialized) return;
    if (feature == null) return;
    if (layerId == 'unselected-clusters') {
      await _handleClusterClick(feature);
    } else if (layerId == 'selected-cluster') {
      await _handleClusterClick(feature);
    } else if (layerId == 'unselected-points') {
      await _handleUnselectedPointClick(MarkerFeature.fromJson(feature.asStringDynamicMap!));
    } else if (layerId == 'selected-point') {
      await _handleSelectedPointClick(MarkerFeature.fromJson(feature.asStringDynamicMap!));
    }
    onSelectedFeaturesChanged?.call(selectedFeatures);
    return;
  }

  Future<void> _handleClusterClick(final Map<String?, Object?> cluster) async {
    try {
      final points = await mapboxMap.getGeoJsonClusterLeaves(sourceId, cluster, null, null);
      final features =
          points.featureCollection?.map((e) => MarkerFeature.fromJson(e.asStringDynamicMap!)).toList() ?? [];
      final clusterIventIds = features.map((e) => e.id).toList();

      if (selectedIventIds.every((e) => clusterIventIds.contains(e)) &&
          selectedIventIds.length == clusterIventIds.length) {
        selectedFeatures.forEach((e) => e.properties.isSelected = false);
        await mapboxMap.style.updateGeoJSONSourceFeatures(
          sourceId,
          'unselected-cluster',
          selectedFeatures.map((e) => Feature.fromJson(e.toJson())).toList(),
        );
        selectedFeatures.clear();
      } else {
        await mapboxMap.style.updateGeoJSONSourceFeatures(
          sourceId,
          'unselected-cluster',
          _getFeaturesToUpdate(features),
        );
      }

      final clusterFeature = cluster.asStringDynamicMap!;
      _flyTo(List<double>.from(clusterFeature['geometry']['coordinates']));
    } catch (e) {
      print('Error handling unselected cluster click: $e');
    }
  }

  Future<void> _handleUnselectedPointClick(final MarkerFeature feature) async {
    try {
      await mapboxMap.style.updateGeoJSONSourceFeatures(
        sourceId,
        'unselected-point',
        _getFeaturesToUpdate([feature]),
      );
      _flyTo(List<double>.from(feature.geometry.coordinates));
    } catch (e) {
      print('Error handling unselected point click: $e');
    }
  }

  Future<void> _handleSelectedPointClick(final MarkerFeature feature) async {
    try {
      final iventId = feature.id;
      feature.properties.isSelected = false;

      await mapboxMap.style.updateGeoJSONSourceFeatures(
        sourceId,
        'selected-point',
        [Feature.fromJson(feature.toJson())],
      );
      selectedFeatures.removeWhere((e) => e.id == iventId);
    } catch (e) {
      print('Error handling selected point click: $e');
    }
  }

  List<Feature> _getFeaturesToUpdate(List<MarkerFeature> newSelectedFeatures) {
    selectedFeatures.forEach((e) => e.properties.isSelected = false);
    newSelectedFeatures.forEach((e) => e.properties.isSelected = true);
    final featuresToUpdate = newSelectedFeatures.toList();
    for (final feature in selectedFeatures) {
      final iventId = feature.id;
      if (!newSelectedFeatures.any((e) => e.id == iventId)) {
        featuresToUpdate.add(feature);
      }
    }
    selectedFeatures.assignAll(newSelectedFeatures);
    return featuresToUpdate.map((e) => Feature.fromJson(e.toJson())).toList();
  }

  void _flyTo(List<double> coordinates) async {
    final zoom = await mapboxMap.getCameraState().then((cameraState) => cameraState.zoom);
    mapboxMap.flyTo(
      CameraOptions(center: Point(coordinates: Position(coordinates[0], coordinates[1])), zoom: zoom),
      MapAnimationOptions(duration: 50),
    );
  }

  void addMarkers(List<MarkerFeature> features) async {
    if (!isMapboxMapInitialized) return;
    if (features.isEmpty) return;
    allFeatures.addAll(features);
    await mapboxMap.style.addGeoJSONSourceFeatures(
      sourceId,
      'add-markers',
      features.map((e) => Feature.fromJson(e.toJson())).toList(),
    );
  }

  void updateMarkers(List<MarkerFeature> features) async {
    if (!isMapboxMapInitialized) return;
    if (features.isEmpty) return;
    allFeatures.removeWhere((e) => features.any((f) => f.id == e.id));
    allFeatures.addAll(features);
    selectedFeatures.removeWhere((e) => features.any((f) => f.id == e.id));
    await mapboxMap.style.updateGeoJSONSourceFeatures(
      sourceId,
      'update-markers',
      features.map((e) => Feature.fromJson(e.toJson())).toList(),
    );
  }

  void removeMarkers(List<String> featureIds) async {
    if (!isMapboxMapInitialized) return;
    if (featureIds.isEmpty) return;
    if (!allFeatures.any((e) => featureIds.contains(e.id))) return;
    allFeatures.removeWhere((e) => featureIds.contains(e.id));
    selectedFeatures.removeWhere((e) => featureIds.contains(e.id));
    await mapboxMap.style.removeGeoJSONSourceFeatures(sourceId, 'remove-markers', featureIds);
  }

  void clearMarkers() {
    allFeatures.clear();
    selectedFeatures.clear();
  }
}
