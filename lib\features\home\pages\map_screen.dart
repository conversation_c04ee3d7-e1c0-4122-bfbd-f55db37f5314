import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/composite/buttons/home_buttons.dart';
import 'package:ivent_app/features/home/<USER>/home_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_map_controller.dart';
import 'package:ivent_app/features/home/<USER>/map_ivent_box.dart';
import 'package:ivent_app/features/home/<USER>/map_screen_date_picker.dart';
import 'package:ivent_app/features/mapbox/widgets/ia_map_widget.dart';

class MapScreen extends StatefulWidget {
  const MapScreen({super.key});

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  // Controllers
  final HomeController _controller = Get.find();

  HomeMapController get homeMapController => _controller.homeMapController;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        _buildMapWidget(),
        _buildDatePicker(),
        _buildActionButtons(),
        _buildIventBox(),
      ],
    );
  }

  IaMapWidget _buildMapWidget() {
    return IaMapWidget(
      onMapCreated: homeMapController.mapboxController.setMapboxMap,
      onBoundsChanged: homeMapController.updateVisibleMapBounds,
      onFeatureSelected: homeMapController.markerController.handleFeatureClick,
    );
  }

  Widget _buildIventBox() {
    return Obx(() {
      final iventBanners = _controller.homeMapController.iventBanners;
      if (iventBanners.isEmpty) return const SizedBox.shrink();
      return Positioned(
        bottom: 170,
        left: 0,
        child: Container(
          height: 190,
          width: min(
            iventBanners.length * 190 * 0.87 +
                (iventBanners.length - 1) * AppDimensions.padding8 +
                2 * AppDimensions.padding20,
            Get.width,
          ),
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            physics: const BouncingScrollPhysics(),
            shrinkWrap: true,
            padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
            itemCount: iventBanners.length,
            itemBuilder: (context, index) {
              final iventBanner = iventBanners[index];
              return MapIventBox(iventBanner: iventBanner);
            },
            separatorBuilder: (context, index) => const SizedBox(width: AppDimensions.padding8),
          ),
        ),
      );
    });
  }

  Positioned _buildDatePicker() {
    return Positioned(
      left: 20,
      top: 20,
      child: MapScreenDatePicker(controller: _controller),
    );
  }

  Positioned _buildActionButtons() {
    return Positioned(
      right: 20,
      bottom: 170,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          HomeButtons.findUserLocation(onTap: _controller.homeMapController.mapboxController.moveCameraToUserLocation),
          const SizedBox(height: AppDimensions.padding8),
          HomeButtons.createIventButtonL(),
        ],
      ),
    );
  }
}
