import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_state_manager.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';

class UserInfoController extends BaseControllerWithSharedState<ProfileStateManager> {
  UserInfoController(AuthService authService, ProfileStateManager state) : super(authService, state);

  final _userPageInfo = Rxn<GetUserByUserIdReturn>();
  final _isFollowing = false.obs;
  final _isFriend = false.obs;

  GetUserByUserIdReturn? get userPageInfo => _userPageInfo.value;
  bool get isFollowing => _isFollowing.value;
  bool get isFriend => _isFriend.value;

  set userPageInfo(GetUserByUserIdReturn? value) => _userPageInfo.value = value;
  set isFollowing(bool value) => _isFollowing.value = value;
  set isFriend(bool value) => _isFriend.value = value;

  @override
  void initController() async {
    super.initController();
    userPageInfo = await authService.usersApi.getByUserId(state.userId);
    if (userPageInfo != null) {
      isFollowing = userPageInfo!.isFollowing;
      isFriend = userPageInfo!.isFriend;
      state.userRole = userPageInfo!.userRole;
    }
  }

  void toggleFollowing() async {
    if (isFollowing) {
      isFollowing = false;
      await authService.usersApi.unfollowByUserId(state.userId);
    } else {
      isFollowing = true;
      await authService.usersApi.followByUserId(state.userId);
    }
  }

  void toggleFriendship() async {
    if (isFriend) {
      isFriend = false;
      await authService.userRelationshipsApi.removeFriendByUserId(state.userId);
    } else {
      isFriend = true;
      await authService.userRelationshipsApi.inviteFriendByUserId(state.userId);
    }
  }
}
