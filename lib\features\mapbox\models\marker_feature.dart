class MarkerFeature {
  final String type = 'Feature';
  final String id;
  final MarkerFeatureProperties properties;
  final MarkerFeatureGeometry geometry;

  MarkerFeature({
    required this.id,
    required this.properties,
    required this.geometry,
  });

  factory MarkerFeature.fromJson(json) {
    return MarkerFeature(
      id: json['id'],
      properties: MarkerFeatureProperties.fromJson(json['properties']),
      geometry: MarkerFeatureGeometry.fromJson(json['geometry']),
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'type': type,
      'id': id,
      'properties': properties.toJson(),
      'geometry': geometry.toJson(),
    };
    return jsonObject;
  }

  @override
  String toString() => 'MarkerFeature(type: $type, id: $id, properties: $properties, geometry: $geometry)';
}

class MarkerFeatureProperties {
  bool isSelected;

  MarkerFeatureProperties({
    required this.isSelected,
  });

  factory MarkerFeatureProperties.from<PERSON>son(json) {
    return MarkerFeatureProperties(
      isSelected: json['isSelected'],
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'isSelected': isSelected,
    };
    return jsonObject;
  }

  @override
  String toString() => 'MarkerFeatureProperties(isSelected: $isSelected)';
}

class MarkerFeatureGeometry {
  final String type = 'Point';

  /// [longitude, latitude]
  final List<double> coordinates;

  MarkerFeatureGeometry({
    required this.coordinates,
  });

  factory MarkerFeatureGeometry.fromJson(json) {
    return MarkerFeatureGeometry(
      coordinates: List<double>.from(json['coordinates']),
    );
  }

  Map<String, dynamic> toJson() {
    final jsonObject = <String, dynamic>{
      'type': type,
      'coordinates': coordinates,
    };
    return jsonObject;
  }

  @override
  String toString() => 'MarkerFeatureGeometry(type: $type, coordinates: $coordinates)';
}
